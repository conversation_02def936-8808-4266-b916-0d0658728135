﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Authorization
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Security;
using HslCommunication.Enthernet;
using HslCommunication.Language;
using HslCommunication.MQTT;
using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading;

#nullable disable
namespace HslCommunication;

/// <summary>系统的基本授权类</summary>
public class Authorization
{
  private static bool nasogaghgaknnc = false;
  private static readonly string Declaration = "禁止对本组件进行反编译，篡改源代码，如果用于商业用途，将追究法律责任，如需注册码，请联系作者，QQ号：200962190，邮箱：<EMAIL>，欢迎企业合作。";
  private static DateTime niahdiahduasdbubfas = DateTime.Now;
  internal static long naihsdadaasdasdiwasdaid = 0;
  internal static long moashdawidaisaosdas = 0;
  internal static double nuasgdawydbishcgas = 8.0;
  internal static int nuasgdaaydbishdgas = 0;
  internal static int nuasgdawydbishdgas = 8;
  internal static double nuasgdawydaishdgas = 24.0;
  internal static int nasidhadguawdbasd = 1000;
  internal static int niasdhasdguawdwdad = 12345;
  internal static int hidahwdauushduasdhu = 23456;
  internal static long iahsduiwikaskfhishfdi = 0;
  internal static int zxnkasdhiashifshfsofh = 0;
  internal static int aosghasdugfavugaosidhaisf = 0;

  static Authorization()
  {
    Authorization.niahdiahduasdbubfas = Authorization.nashgaosgasaisfasfga();
    if (Authorization.naihsdadaasdasdiwasdaid != 0L)
      Authorization.naihsdadaasdasdiwasdaid = 0L;
    if (Authorization.nuasgdawydaishdgas != 24.0)
      Authorization.nuasgdawydaishdgas = 24.0;
    if (Authorization.nuasgdaaydbishdgas != 0)
      Authorization.nuasgdaaydbishdgas = 0;
    if (Authorization.nuasgdawydbishdgas == 24)
      return;
    Authorization.nuasgdawydbishdgas = 24;
  }

  private static void asidhiahfaoaksdnasoif(object obj)
  {
    for (int index = 0; index < 3600; ++index)
    {
      HslHelper.ThreadSleep(1000);
      if (Authorization.naihsdadaasdasdiwasdaid == (long) Authorization.niasdhasdguawdwdad && Authorization.nuasgdaaydbishdgas > 0)
        return;
    }
    new NetSimplifyClient("118.24.36.220", 18467).ReadCustomerFromServer((NetHandle) 500, SoftBasic.FrameworkVersion.ToString());
  }

  private static void noajfojgkansdnfgaggh()
  {
    if (Authorization.nasogaghgaknnc)
      return;
    if (Authorization.niahdiahduasdbubfas < new DateTime(1980, 1, 1) && Authorization.nashgaosgasaisfasfga() >= new DateTime(1980, 1, 1))
    {
      Authorization.niahdiahduasdbubfas = Authorization.nashgaosgasaisfasfga();
      Authorization.nasogaghgaknnc = true;
    }
    else if (Authorization.niahdiahduasdbubfas >= new DateTime(1980, 1, 1) && Authorization.nashgaosgasaisfasfga() >= new DateTime(1980, 1, 1))
      Authorization.nasogaghgaknnc = true;
  }

  /// <summary>普通VIP的授权的判定</summary>
  /// <returns>是否授权成功</returns>
  internal static bool nzugaydgwadawdibbas()
  {
    ++Authorization.moashdawidaisaosdas;
    if (Authorization.naihsdadaasdasdiwasdaid == (long) Authorization.niasdhasdguawdwdad && Authorization.nuasgdaaydbishdgas > 0)
      return Authorization.nuasduagsdwydbasudasd();
    Authorization.noajfojgkansdnfgaggh();
    return (Authorization.nashgaosgasaisfasfga() - Authorization.niahdiahduasdbubfas).TotalHours < Authorization.nuasgdawydaishdgas ? Authorization.nuasduagsdwydbasudasd() : Authorization.asdhuasdgawydaduasdgu();
  }

  /// <summary>商业授权则返回true，否则返回false</summary>
  /// <returns>是否成功进行商业授权</returns>
  internal static bool asdniasnfaksndiqwhawfskhfaiw()
  {
    if (Authorization.naihsdadaasdasdiwasdaid == (long) Authorization.niasdhasdguawdwdad && Authorization.nuasgdaaydbishdgas > 0)
      return Authorization.nuasduagsdwydbasudasd();
    Authorization.noajfojgkansdnfgaggh();
    return (Authorization.nashgaosgasaisfasfga() - Authorization.niahdiahduasdbubfas).TotalHours < (double) Authorization.nuasgdawydbishdgas ? Authorization.nuasduagsdwydbasudasd() : Authorization.asdhuasdgawydaduasdgu();
  }

  internal static bool nuasduagsdwydbasudasd() => true;

  internal static bool asdhuasdgawydaduasdgu() => false;

  internal static bool ashdadgawdaihdadsidas() => Authorization.niasdhasdguawdwdad == 12345;

  internal static DateTime nashgaosgasaisfasfga() => DateTime.Now;

  internal static DateTime iashdagsaawbdawda() => DateTime.Now.AddDays(1.0);

  internal static DateTime iashdagsaawadawda() => DateTime.Now.AddDays(2.0);

  internal static void oasjodaiwfsodopsdjpasjpf()
  {
    Interlocked.Increment(ref Authorization.iahsduiwikaskfhishfdi);
  }

  internal static string nasduabwduadawdb(string miawdiawduasdhasd)
  {
    StringBuilder stringBuilder = new StringBuilder();
    MD5 md5 = MD5.Create();
    byte[] hash = md5.ComputeHash(Encoding.Unicode.GetBytes(miawdiawduasdhasd));
    md5.Clear();
    for (int index = 0; index < hash.Length; ++index)
      stringBuilder.Append(((int) byte.MaxValue - (int) hash[index]).ToString("X2"));
    return stringBuilder.ToString();
  }

  /// <summary>
  /// 通过指定的IP地址，端口号信息的接口服务器获取密钥，然后进行激活，适用局域网内一个主服务对其他电脑的激活操作。如果网络失败或是密钥不正确，则激活失败。<br />
  /// The interface server obtains the key through the specified IP address and port number information, and then activates it,
  /// which applies to the activation operation of one main service in the LAN to other computers. If the network fails or the key is incorrect, activation fails.
  /// </summary>
  /// <param name="ip">远程的IP地址信息</param>
  /// <param name="port">远程的端口号信息</param>
  /// <param name="token">令牌信息，不是必须的，具体取决于服务器作用</param>
  /// <returns>是否激活成功</returns>
  public static bool SetAuthorizationCode(string ip, int port, string token = null)
  {
    MqttSyncClient mqttSyncClient = new MqttSyncClient(new MqttConnectionOptions()
    {
      IpAddress = ip,
      Port = port,
      UseRSAProvider = true
    });
    RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
    OperateResult<string> operateResult = mqttSyncClient.ReadRpc<string>("GetLicense", (object) new
    {
      pubKey = Convert.ToBase64String(rsa.GetPEMPublicKey()),
      token = token
    });
    return operateResult.IsSuccess ? Authorization.SetAuthorizationCode(Encoding.UTF8.GetString(rsa.DecryptLargeData(Convert.FromBase64String(operateResult.Content)))) : Authorization.asdhuasdgawydaduasdgu();
  }

  /// <summary>
  /// 设置本组件激活失败的情况时候的联系方式，可以设置成自定义的字符串，当且仅当企业用户激活成功后调用本方法有效，如果不设置默认为：联系QQ200962190，微信：13516702732，Email:<EMAIL><br />
  /// Set the contact information when the activation of this component fails, can be set to a custom string,
  /// if and only if the enterprise user activation is successful to call this method is valid, if not set the default is: contact QQ200962190, WeChat: 13516702732,Email:<EMAIL>
  /// </summary>
  /// <param name="contact">新的联系方式</param>
  public static void SetDllContact(string contact) => DefaultLanguage.Contact = contact;

  /// <summary>
  /// 设置本组件系统的授权信息，如果激活失败，只能使用24小时，24小时后所有的网络通信不会成功<br />
  /// Set the authorization information of this component system. If the activation fails, it can only be used for 8 hours. All network communication will not succeed after 8 hours
  /// </summary>
  /// <param name="code">授权码，如果授权码为空，则使用在线激活的方式</param>
  /// <returns>是否激活成功</returns>
  public static bool SetAuthorizationCode(string code)
  {
    if (string.IsNullOrEmpty(code))
    {
      string info = SoftAuthorize.GetInfo(false);
      RSACryptoServiceProvider providerFromPublicKey = RSAHelper.CreateRsaProviderFromPublicKey(Convert.FromBase64String("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCfrMKxXexJVc/2/J9CRfq+uTxvlTLqnLXvXKbXBdaDWIanJv3D0rODNdTgedEo1gTWOzpd2lJRQ7t8ghRU7eGUFj2honEMYwQv/Z22Q9LHZgZ8BvuizOthsKFRZUzn3tzmX0hOt/T9knsT4gML7FQaXnMMRHtTmIjW1VGHw1pemQIDAQAB"));
      OperateResult<bool> operateResult = new MqttSyncClient(new MqttConnectionOptions()
      {
        IpAddress = "***************",
        Port = 7808
      }).ReadRpc<bool>("CheckLicense", (object) new
      {
        code = Convert.ToBase64String(providerFromPublicKey.EncryptLargeData(Encoding.UTF8.GetBytes(info)))
      });
      if (!operateResult.IsSuccess || !operateResult.Content)
        return Authorization.asdhuasdgawydaduasdgu();
      Authorization.nuasgdaaydbishdgas = 10000;
      Authorization.nuasgdawydbishcgas = (double) Authorization.nuasgdawydbishdgas;
      Authorization.naihsdadaasdasdiwasdaid = (long) Authorization.niasdhasdguawdwdad;
      return Authorization.naihsdadaasdasdiwasdaid == (long) Authorization.niasdhasdguawdwdad && Authorization.nuasgdaaydbishdgas > 0;
    }
    string code1 = Authorization.nasduabwduadawdb(code);
    if (code1 == "D3DA2DC58A442D85D2C17BCDF5A228CE")
    {
      Authorization.nuasgdaaydbishdgas = 1;
      Authorization.nuasgdawydbishcgas = 286512937.0;
      Authorization.nuasgdawydaishdgas = 87600.0;
      return Authorization.nuasduagsdwydbasudasd();
    }
    if (!Authorization.isActiveCodeEnterprisenvasfaosg(code1))
      return Authorization.asdhuasdgawydaduasdgu();
    Authorization.nuasgdaaydbishdgas = 10000;
    Authorization.nuasgdawydbishcgas = (double) Authorization.nuasgdawydbishdgas;
    Authorization.naihsdadaasdasdiwasdaid = (long) Authorization.niasdhasdguawdwdad;
    return Authorization.naihsdadaasdasdiwasdaid == (long) Authorization.niasdhasdguawdwdad && Authorization.nuasgdaaydbishdgas > 0;
  }

  /// <summary>
  /// 使用证书激活hslcommunication组件，证书请联系胡工科技获取，如果获取的是文件的话，那就是 SetHslCertificate( File.ReadAllBytes( "hsl.cert" ) ) 获取原始字节信息<br />
  /// Use the certificate to activate the hslcommunication component, please contact Hugong Technology to obtain the certificate, if the obtained is a file,
  /// that is, SetHslCertificate( File.ReadAllBytes( "hsl.cert") ) to obtain the raw byte information
  /// </summary>
  /// <param name="cert">证书信息</param>
  /// <returns>是否激活成功</returns>
  public static OperateResult SetHslCertificate(byte[] cert)
  {
    if (!HslCertificate.VerifyCer(Convert.FromBase64String("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCoKsX100Id/uNzOuR9GhBiaXBfMqYBzyr9tGF55ZdP6Wsm51yizuiiEKK8tV5nTDo6mtx3O7gyNLECDHsO2OMxorF4cNNMCME5Mc7pzsryDNOtGJrULpTi6WWcXGK33W3Tx2gsvLTIP+eFKSfk3/hkBOjQiVlBN9tV/pgVL2zG9QIDAQAB"), cert))
      return new OperateResult("Certificate verify failed!");
    HslCertificate from = HslCertificate.CreateFrom(cert);
    if (from.KeyWord != "HslCommunication")
      return new OperateResult("Certificate key word not correct!");
    if (!(from.NotBefore <= Authorization.nashgaosgasaisfasfga()) || !(Authorization.nashgaosgasaisfasfga() <= from.NotAfter))
      return new OperateResult("Certificate time overdue");
    if (!string.IsNullOrEmpty(from.UniqueID) && !(from.UniqueID == Environment.MachineName))
    {
      bool UseAdmin = false;
      bool useHDD = true;
      if (from.Descriptions != null)
      {
        if (from.Descriptions.ContainsKey("UseAdmin"))
          UseAdmin = from.Descriptions["UseAdmin"] == "1";
        if (from.Descriptions.ContainsKey("UseHDD"))
          useHDD = from.Descriptions["UseHDD"] == "1";
      }
      if (SoftAuthorize.GetInfo(UseAdmin, useHDD) != from.UniqueID)
        return new OperateResult("Certificate uniqueID check failed");
    }
    if (from.EffectiveHours <= Authorization.aosghasdugfavugaosidhaisf)
    {
      Authorization.nuasgdaaydbishdgas = 10000;
      Authorization.nuasgdawydbishcgas = (double) Authorization.nuasgdawydbishdgas;
      Authorization.naihsdadaasdasdiwasdaid = (long) Authorization.niasdhasdguawdwdad;
      return Authorization.naihsdadaasdasdiwasdaid != (long) Authorization.niasdhasdguawdwdad || Authorization.nuasgdaaydbishdgas <= 0 ? new OperateResult("Unknown Failed") : OperateResult.CreateSuccessResult();
    }
    Authorization.nuasgdawydaishdgas = (double) from.EffectiveHours;
    Authorization.nuasgdawydbishdgas = from.EffectiveHours;
    return OperateResult.CreateSuccessResult();
  }

  private static bool isActiveCodeEnterprisenvasfaosg(string code)
  {
    // 简化的授权检查逻辑
    return !string.IsNullOrEmpty(code);
  }
}
