﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.SiemensS7Plus
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.Siemens.S7PlusHelper;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <summary>测试的基于S7 PLUS的协议，功能还未完成，无法调用</summary>
public class SiemensS7Plus : DeviceTcpNet
{
  private byte[] iso_head = new byte[20]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 36,
    (byte) 31 /*0x1F*/,
    (byte) 224 /*0xE0*/,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 192 /*0xC0*/,
    (byte) 1,
    (byte) 10,
    (byte) 193,
    (byte) 2,
    (byte) 1,
    (byte) 0,
    (byte) 194,
    (byte) 16 /*0x10*/
  };
  private byte[] destTSAP = Encoding.ASCII.GetBytes("SIMATIC-ROOT-HMI");
  private int pdu_length = 240 /*0xF0*/;
  private SoftIncrementCount incrementCount;
  private SoftIncrementCount integrityIdCount;
  private byte protocolVersion;
  private bool useSSL = false;
  private OpenSslConnect sslConnect;
  private MemoryStream memoryStream = new MemoryStream();
  private AutoResetEvent autoResetEventPdu;
  private byte protoVersionBuffer = 2;
  private byte[] recieveBuffer;

  /// <summary>
  /// 实例化一个西门子的S7协议的通讯对象 <br />
  /// Instantiate a communication object for a Siemens S7 protocol
  /// </summary>
  public SiemensS7Plus()
  {
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
    this.integrityIdCount = new SoftIncrementCount((long) uint.MaxValue);
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.autoResetEventPdu = new AutoResetEvent(false);
    this.ReceiveTimeOut = 30000;
  }

  /// <summary>
  /// 实例化一个西门子的S7协议的通讯对象并指定Ip地址 <br />
  /// Instantiate a communication object for a Siemens S7 protocol and specify an IP address
  /// </summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号信息</param>
  public SiemensS7Plus(string ipAddress, int port = 102)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new S7Message();

  /// <summary>
  /// 西门子相关的本地TSAP参数信息<br />
  /// A parameter information related to Siemens
  /// </summary>
  public ushort LocalTSAP { get; set; } = 1536 /*0x0600*/;

  /// <summary>
  /// 西门子相关的远程TSAP参数信息<br />
  /// A parameter information related to Siemens
  /// </summary>
  public byte[] DestTSAP
  {
    get => this.destTSAP;
    set => this.destTSAP = value;
  }

  /// <summary>最近一次的PDU类型</summary>
  public byte LastPDUType { get; set; }

  /// <summary>
  /// 获取当前西门子的PDU的长度信息，不同型号PLC的值会不一样。<br />
  /// Get the length information of the current Siemens PDU, the value of different types of PLC will be different.
  /// </summary>
  public int PDULength => this.pdu_length;

  /// <summary>获取当前的通信会话ID信息</summary>
  public uint SessionID { get; private set; }

  /// <summary>获取当前的PLC的订货号</summary>
  public string OrderNumber { get; private set; }

  private OperateResult<byte[]> ReadFromCoreServer(
    CommunicationPipe pipe,
    List<byte[]> sends,
    bool hasResponseData = true,
    bool usePackHeader = true)
  {
    INetMessage newNetMessage = this.GetNewNetMessage();
    if (newNetMessage != null)
      newNetMessage.SendBytes = sends.Count > 0 ? sends[0] : (byte[]) null;
    OperateResult result = this.sslConnect.Write(sends);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result);
    if (this.ReceiveTimeOut < 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!hasResponseData)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (this.SleepTime > 0)
      HslHelper.ThreadSleep(this.SleepTime);
    if (this.autoResetEventPdu.WaitOne(this.ReceiveTimeOut))
    {
      newNetMessage.HeadBytes = this.recieveBuffer;
      OperateResult<byte[]> successResult = OperateResult.CreateSuccessResult<byte[]>(this.recieveBuffer);
      return !successResult.IsSuccess ? successResult : OperateResult.CreateSuccessResult<byte[]>(successResult.Content);
    }
    pipe.RaisePipeError();
    pipe.CloseCommunication();
    this.sslConnect?.Dispose();
    return new OperateResult<byte[]>(-10000, StringResources.Language.ReceiveDataTimeout + this.ReceiveTimeOut.ToString());
  }

  private byte[] PackSendBuffer(byte[] buffer)
  {
    this.ByteTransform.TransByte(this.SessionID).CopyTo((Array) buffer, 9);
    MemoryStream ms = new MemoryStream();
    ms.Write(buffer);
    ms.Write("00 00 04 E8 89 69 00 12 00 00 00 00 89 6A 00 13 00 89 6B 00 04 00 00".ToHexBytes());
    uint currentValue1 = (uint) this.integrityIdCount.GetCurrentValue();
    S7Object.WriteUint32(ms, currentValue1);
    ms.Write("00 00 00 00".ToHexBytes());
    buffer = ms.ToArray();
    int currentValue2 = (int) this.incrementCount.GetCurrentValue();
    buffer[7] = BitConverter.GetBytes(currentValue2)[1];
    buffer[8] = BitConverter.GetBytes(currentValue2)[0];
    return buffer;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackAndUnpack = true)
  {
    if (!this.useSSL)
    {
      OperateResult<byte[]> operateResult;
      byte[] content;
      do
      {
        operateResult = base.ReadFromCoreServer(pipe, send, hasResponseData, usePackAndUnpack);
        if (operateResult.IsSuccess)
          content = operateResult.Content;
        else
          goto label_1;
      }
      while (content == null || content.Length < 4 || (int) operateResult.Content[2] * 256 /*0x0100*/ + (int) operateResult.Content[3] == 7);
      goto label_3;
label_1:
      return operateResult;
label_3:
      this.LastPDUType = operateResult.Content[5];
      return operateResult;
    }
    if (send[0] != (byte) 114)
    {
      if (usePackAndUnpack)
        send = this.PackSendBuffer(send);
      return this.ReadFromCoreServer(pipe, this.BuildS7PlusPdu(send, send.Length, (byte) 2), hasResponseData, usePackAndUnpack);
    }
    CommunicationPipe pipe1 = pipe;
    List<byte[]> sends = new List<byte[]>();
    sends.Add(send);
    int num1 = hasResponseData ? 1 : 0;
    int num2 = usePackAndUnpack ? 1 : 0;
    return this.ReadFromCoreServer(pipe1, sends, num1 != 0, num2 != 0);
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    this.useSSL = false;
    this.CommunicationPipe.UseServerActivePush = false;
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
    this.integrityIdCount = new SoftIncrementCount((long) uint.MaxValue);
    this.SessionID = 0U;
    this.CommunicationPipe.ResetConnectErrorCount();
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(this.CommunicationPipe, this.GetISOTelegrams(), true, false);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    if (operateResult1.Content.Length == 36 && this.LastPDUType != (byte) 208 /*0xD0*/)
      return new OperateResult(65536 /*0x010000*/, "errIsoConnect");
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.CommunicationPipe, this.BuildInitSslCommand(), true, false);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    if (this.ByteTransform.TransUInt16(operateResult2.Content, 14) != (ushort) 1459)
    {
      this.CommunicationPipe.CloseCommunication();
      return new OperateResult("InitSsl failed: need 0x05b3 but actual 0x" + this.ByteTransform.TransUInt16(operateResult2.Content, 14).ToString("X"));
    }
    this.protocolVersion = operateResult2.Content[8];
    try
    {
      this.sslConnect?.Dispose();
      this.useSSL = true;
      this.sslConnect = new OpenSslConnect(new Func<byte[], OperateResult>(this.SendS7Pdu), new Action<byte[]>(this.ReceiveS7Pdu));
      this.sslConnect.LogNet = this.LogNet;
    }
    catch (Exception ex)
    {
      return new OperateResult("Create OpenSslConnect failed: " + ex.Message);
    }
    this.CommunicationPipe.UseServerActivePush = true;
    base.InitializationOnConnect();
    try
    {
      this.sslConnect.SSLConnect();
      this.sslConnect.SSLInit();
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
    HslHelper.ThreadSleep(100);
    OperateResult<byte[]> operateResult3 = this.ReadFromCoreServer(this.CommunicationPipe, this.BuildCreateObjectRequest(), true, false);
    if (!operateResult3.IsSuccess)
      return (OperateResult) operateResult3;
    int index1 = 10;
    long valueUint64 = (long) S7Object.GetValueUint64(operateResult3.Content, ref index1);
    int index2 = index1 + 1;
    this.SessionID = S7Object.GetValueUint32(operateResult3.Content, ref index2);
    int valueUint32 = (int) S7Object.GetValueUint32(operateResult3.Content, ref index2);
    int num = index2;
    this.OrderNumber = Encoding.ASCII.GetString(this.ExtraResponseData(operateResult3.Content, 319U, ref index2).Content);
    int index3 = num;
    OperateResult<byte[]> operateResult4 = this.ReadFromCoreServer(this.CommunicationPipe, this.BuildMultiSetRequest(this.ExtraResponseData(operateResult3.Content, 306U, ref index3).Content), true, false);
    return !operateResult4.IsSuccess ? (OperateResult) operateResult4 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override bool DecideWhetherQAMessage(
    CommunicationPipe pipe,
    OperateResult<byte[]> receive)
  {
    byte[] content = receive.Content;
    if (content != null && content.Length >= 4 && (int) receive.Content[2] * 256 /*0x0100*/ + (int) receive.Content[3] != 7 && receive.Content.Length > 7)
    {
      this.sslConnect.ReadCompleted(receive.Content.RemoveBegin<byte>(7));
      if (receive.Content[7] == (byte) 22)
        this.sslConnect.SSLInit();
    }
    return false;
  }

  /// <inheritdoc />
  protected override OperateResult ExtraOnDisconnect()
  {
    this.sslConnect?.Dispose();
    return base.ExtraOnDisconnect();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    this.useSSL = false;
    this.CommunicationPipe.UseServerActivePush = false;
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
    this.integrityIdCount = new SoftIncrementCount((long) uint.MaxValue);
    this.SessionID = 0U;
    this.CommunicationPipe.ResetConnectErrorCount();
    OperateResult<byte[]> read1 = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.GetISOTelegrams(), true, false);
    if (!read1.IsSuccess)
      return (OperateResult) read1;
    if (read1.Content.Length == 36 && this.LastPDUType != (byte) 208 /*0xD0*/)
      return new OperateResult(65536 /*0x010000*/, "errIsoConnect");
    OperateResult<byte[]> read2 = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.BuildInitSslCommand(), true, false);
    if (!read2.IsSuccess)
      return (OperateResult) read2;
    if (this.ByteTransform.TransUInt16(read2.Content, 14) != (ushort) 1459)
    {
      OperateResult operateResult = await this.CommunicationPipe.CloseCommunicationAsync();
      return new OperateResult("InitSsl failed: need 0x05b3 but actual 0x" + this.ByteTransform.TransUInt16(read2.Content, 14).ToString("X"));
    }
    this.protocolVersion = read2.Content[8];
    try
    {
      this.sslConnect?.Dispose();
      this.useSSL = true;
      this.sslConnect = new OpenSslConnect(new Func<byte[], OperateResult>(this.SendS7Pdu), new Action<byte[]>(this.ReceiveS7Pdu));
      this.sslConnect.LogNet = this.LogNet;
    }
    catch (Exception ex)
    {
      return new OperateResult("Create OpenSslConnect failed: " + ex.Message);
    }
    this.CommunicationPipe.UseServerActivePush = true;
    base.InitializationOnConnect();
    try
    {
      this.sslConnect.SSLConnect();
      this.sslConnect.SSLInit();
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
    HslHelper.ThreadSleep(100);
    OperateResult<byte[]> sessionRead = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.BuildCreateObjectRequest(), true, false);
    if (!sessionRead.IsSuccess)
      return (OperateResult) sessionRead;
    int index = 10;
    long valueUint64 = (long) S7Object.GetValueUint64(sessionRead.Content, ref index);
    ++index;
    this.SessionID = S7Object.GetValueUint32(sessionRead.Content, ref index);
    int valueUint32 = (int) S7Object.GetValueUint32(sessionRead.Content, ref index);
    int indexTmp = index;
    this.OrderNumber = Encoding.ASCII.GetString(this.ExtraResponseData(sessionRead.Content, 319U, ref index).Content);
    index = indexTmp;
    byte[] structValue = this.ExtraResponseData(sessionRead.Content, 306U, ref index).Content;
    OperateResult<byte[]> multiSet = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.BuildMultiSetRequest(structValue), true, false);
    return multiSet.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) multiSet;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackAndUnpack = true)
  {
    if (!this.useSSL)
    {
      OperateResult<byte[]> read;
      while (true)
      {
        read = await base.ReadFromCoreServerAsync(pipe, send, hasResponseData, usePackAndUnpack);
        if (read.IsSuccess)
        {
          byte[] content = read.Content;
          if (content == null || content.Length < 4 || (int) read.Content[2] * 256 /*0x0100*/ + (int) read.Content[3] == 7)
            read = (OperateResult<byte[]>) null;
          else
            goto label_4;
        }
        else
          break;
      }
      return read;
label_4:
      this.LastPDUType = read.Content[5];
      return read;
    }
    if (send[0] != (byte) 114)
    {
      if (usePackAndUnpack)
        send = this.PackSendBuffer(send);
      OperateResult<byte[]> operateResult = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() => this.ReadFromCoreServer(pipe, this.BuildS7PlusPdu(send, send.Length, (byte) 2), hasResponseData, usePackAndUnpack)));
      return operateResult;
    }
    OperateResult<byte[]> operateResult1 = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() =>
    {
      CommunicationPipe pipe1 = pipe;
      List<byte[]> sends = new List<byte[]>();
      sends.Add(send);
      int num1 = hasResponseData ? 1 : 0;
      int num2 = usePackAndUnpack ? 1 : 0;
      return this.ReadFromCoreServer(pipe1, sends, num1 != 0, num2 != 0);
    }));
    return operateResult1;
  }

  private S7Tag[] CreateTagsFromAddress(string address, ushort length)
  {
    return this.CreateTagsFromAddress(address.Split(new char[1]
    {
      ';'
    }, StringSplitOptions.RemoveEmptyEntries));
  }

  private S7Tag[] CreateTagsFromAddress(string[] address)
  {
    List<S7Tag> s7TagList = new List<S7Tag>();
    for (int index = 0; index < address.Length; ++index)
    {
      string[] source = address[index].Split(new char[1]
      {
        '.'
      }, StringSplitOptions.RemoveEmptyEntries);
      s7TagList.Add(new S7Tag()
      {
        LID = ((IEnumerable<string>) source).Select<string, uint>((Func<string, uint>) (m => Convert.ToUInt32(m, 16 /*0x10*/))).ToList<uint>()
      });
    }
    return s7TagList.ToArray();
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<List<S7Value>> result = this.ReadTags(this.CreateTagsFromAddress(address, length));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    List<S7Value> content = result.Content;
    if (content == null || content.Count <= 0)
      return new OperateResult<byte[]>("S7 Value null, address not exist!");
    return result.Content.Count == 1 ? OperateResult.CreateSuccessResult<byte[]>(result.Content[0].Buffer) : OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(result.Content.Select<S7Value, byte[]>((Func<S7Value, byte[]>) (m => m.Buffer)).ToArray<byte[]>()));
  }

  /// <inheritdoc />
  /// <remarks>
  /// 此处写入的数据需要带标志位，类型，数据信息，所以灵活的支持任意的数据写入，如果需要写入 uint8[] 数据本身，使用方法 <see cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.WriteByte(System.String,System.Byte[])" />
  /// </remarks>
  public override OperateResult Write(string address, byte[] value)
  {
    return this.WriteTag(address, value);
  }

  /// <inheritdoc />
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<List<S7Value>> result = this.ReadTags(this.CreateTagsFromAddress(address, length));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    List<S7Value> content = result.Content;
    return content != null && content.Count > 0 ? OperateResult.CreateSuccessResult<bool[]>(result.Content[0].Buffer.ToBoolArray().SelectBegin<bool>((int) length)) : new OperateResult<bool[]>("S7 Value null, address not exist!");
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, bool value)
  {
    return this.WriteTag(address, S7Value.GetBufferBool(this.ByteTransform, value));
  }

  /// <summary>使用类型代号为 0x0a 写入到指定的地址里，返回是否写入成功的结果对象</summary>
  /// <param name="address">地址信息</param>
  /// <param name="value">二进制的值</param>
  /// <returns>是否写入成功</returns>
  public OperateResult WriteByte(string address, byte[] value)
  {
    return this.WriteTag(address, S7Value.GetBufferUInt8(this.ByteTransform, value));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<List<S7Value>> read = await this.ReadTagsAsync(this.CreateTagsFromAddress(address, length));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
    List<S7Value> content = read.Content;
    // ISSUE: explicit non-virtual call
    return content == null || content.Count <= 0 ? new OperateResult<byte[]>("S7 Value null, address not exist!") : (read.Content.Count != 1 ? OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(read.Content.Select<S7Value, byte[]>((Func<S7Value, byte[]>) (m => m.Buffer)).ToArray<byte[]>())) : OperateResult.CreateSuccessResult<byte[]>(read.Content[0].Buffer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<List<S7Value>> read = await this.ReadTagsAsync(this.CreateTagsFromAddress(address, length));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    List<S7Value> content = read.Content;
    // ISSUE: explicit non-virtual call
    return content == null || content.Count <= 0 ? new OperateResult<bool[]>("S7 Value null, address not exist!") : OperateResult.CreateSuccessResult<bool[]>(read.Content[0].Buffer.ToBoolArray().SelectBegin<bool>((int) length));
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferBool(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.WriteByte(System.String,System.Byte[])" />
  public async Task<OperateResult> WriteByteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferUInt8(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> result = this.Read(address, length);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result) : OperateResult.CreateSuccessResult<string>(encoding.GetString(result.Content, 2, (int) result.Content[1]));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Byte)" />
  public OperateResult Write(string address, byte value)
  {
    return this.WriteTag(address, S7Value.GetBufferUInt8(this.ByteTransform, value));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, short value)
  {
    return this.WriteTag(address, S7Value.GetBufferInt16(this.ByteTransform, value));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, ushort value)
  {
    return this.WriteTag(address, S7Value.GetBufferUInt16(this.ByteTransform, value));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, int value)
  {
    return this.WriteTag(address, S7Value.GetBufferInt32(this.ByteTransform, value));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, int[] values)
  {
    return this.WriteTag(address, S7Value.GetBufferInt32(this.ByteTransform, values));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, uint value)
  {
    return this.WriteTag(address, S7Value.GetBufferUInt32(this.ByteTransform, value));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, uint[] values)
  {
    return this.WriteTag(address, S7Value.GetBufferUInt32(this.ByteTransform, values));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, float value)
  {
    return this.WriteTag(address, S7Value.GetBufferFloat(this.ByteTransform, value));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, float[] values)
  {
    return this.WriteTag(address, S7Value.GetBufferFloat(this.ByteTransform, values));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, double value)
  {
    return this.WriteTag(address, S7Value.GetBufferDouble(this.ByteTransform, value));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, double[] values)
  {
    return this.WriteTag(address, S7Value.GetBufferDouble(this.ByteTransform, values));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value)
  {
    return this.Write(address, value, Encoding.Default);
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    OperateResult<byte[]> operateResult = this.Read(address, (ushort) 1);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    byte[] bytes = encoding.GetBytes(value);
    if (bytes.Length > (int) operateResult.Content[0])
      return new OperateResult("string length is too big, must less than :" + operateResult.Content[0].ToString());
    bytes.CopyTo((Array) operateResult.Content, 2);
    operateResult.Content[1] = (byte) bytes.Length;
    return (OperateResult) OperateResult.CreateSuccessResult<string>(encoding.GetString(operateResult.Content, 2, (int) operateResult.Content[1]));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, length);
    OperateResult<string> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<string>(encoding.GetString(read.Content, 2, (int) read.Content[1])) : OperateResult.CreateFailedResult<string>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferUInt8(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, short value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferInt16(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, ushort value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferUInt16(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, int value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferInt32(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferInt32(this.ByteTransform, values));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, uint value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferUInt32(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferUInt32(this.ByteTransform, values));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, float value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferFloat(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferFloat(this.ByteTransform, values));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, double value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferDouble(this.ByteTransform, value));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, S7Value.GetBufferDouble(this.ByteTransform, values));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, string value)
  {
    OperateResult operateResult = await this.WriteAsync(address, value, Encoding.Default);
    return operateResult;
  }

  /// <summary>
  /// 浏览当前PLC的DB块列表，并且拿到用户自定义的DB块的关联ID信息<br />
  /// Browse the DB block list of the current PLC and get the associated ID information of the user-defined DB block
  /// </summary>
  /// <returns>DB块列表信息</returns>
  public OperateResult<List<S7Object>> BrowerDB()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildBrowseRequest(3U, new uint[3]
    {
      233U,
      2521U,
      4288U
    })[0]);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<List<S7Object>>((OperateResult) result1);
    List<S7Object> s7ObjectList = this.ExploreResponse(result1.Content);
    for (int index1 = 0; index1 < s7ObjectList.Count; ++index1)
    {
      S7Object[] array = s7ObjectList[index1].SubObjects.Where<S7Object>((Func<S7Object, bool>) (m => m.RelationId >> 16 /*0x10*/ == 35342U)).ToArray<S7Object>();
      OperateResult<byte[]> result2 = this.ReadFromCoreServer(this.BuildMultiGetRequest((IS7Object[]) array));
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<List<S7Object>>((OperateResult) result2);
      for (int index2 = 0; index2 < array.Length; ++index2)
        array[index2].RelationId2 = this.ByteTransform.TransUInt32(result2.Content, 7 * index2 + 3 + 11);
    }
    return OperateResult.CreateSuccessResult<List<S7Object>>(s7ObjectList);
  }

  /// <summary>浏览PLC的点位信息</summary>
  /// <returns>点位结果列表</returns>
  public OperateResult<List<S7Object>> BrowseTags()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildBrowseRequest(537U, new uint[0])[0]);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<List<S7Object>>((OperateResult) result) : OperateResult.CreateSuccessResult<List<S7Object>>(this.ExploreResponse(result.Content));
  }

  /// <summary>批量读取多个节点数据信息，需要传入节点数组</summary>
  /// <param name="s7Tags">节点数组信息</param>
  /// <returns>成功的结果列表</returns>
  public OperateResult<List<S7Value>> ReadTags(S7Tag[] s7Tags)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildMultiGetRequest((IS7Object[]) s7Tags));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<List<S7Value>>((OperateResult) result) : this.ExploreS7Values(s7Tags, result.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.ReadTags(HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag[])" />
  public OperateResult<List<S7Value>> ReadTags(string[] ids)
  {
    return this.ReadTags(this.CreateTagsFromAddress(ids));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.WriteTag(HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag[],System.Collections.Generic.List{System.Byte[]})" />
  public OperateResult WriteTag(string id, byte[] buffer)
  {
    return this.WriteTag(new string[1]{ id }, new List<byte[]>()
    {
      buffer
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.WriteTag(HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag[],System.Collections.Generic.List{System.Byte[]})" />
  public OperateResult WriteTag(string[] ids, List<byte[]> buffers)
  {
    return this.WriteTag(this.CreateTagsFromAddress(ids), buffers);
  }

  /// <summary>将实际的数据写入到标签中去，支持批量写入的操作。需要传入实际的点位地址，以及封装后的数据内容。</summary>
  /// <param name="ids">点位信息</param>
  /// <param name="buffers">封装过的数据</param>
  /// <returns>是否写入成功</returns>
  public OperateResult WriteTag(S7Tag[] ids, List<byte[]> buffers)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildMultiSetRequest((IS7Object[]) ids, buffers));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<List<S7Value>>((OperateResult) result) : this.CheckReturnCode(result.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.ReadTags(HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag[])" />
  public async Task<OperateResult<List<S7Value>>> ReadTagsAsync(S7Tag[] s7Tags)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildMultiGetRequest((IS7Object[]) s7Tags));
    OperateResult<List<S7Value>> operateResult = read.IsSuccess ? this.ExploreS7Values(s7Tags, read.Content) : OperateResult.CreateFailedResult<List<S7Value>>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.ReadTags(HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag[])" />
  public async Task<OperateResult<List<S7Value>>> ReadTagsAsync(string[] ids)
  {
    OperateResult<List<S7Value>> operateResult = await this.ReadTagsAsync(this.CreateTagsFromAddress(ids));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.WriteTag(HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag[],System.Collections.Generic.List{System.Byte[]})" />
  public async Task<OperateResult> WriteTagAsync(string id, byte[] buffer)
  {
    OperateResult operateResult = await this.WriteTagAsync(new string[1]
    {
      id
    }, new List<byte[]>() { buffer });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.WriteTag(HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag[],System.Collections.Generic.List{System.Byte[]})" />
  public async Task<OperateResult> WriteTagAsync(string[] ids, List<byte[]> buffers)
  {
    OperateResult operateResult = await this.WriteTagAsync(this.CreateTagsFromAddress(ids), buffers);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Plus.WriteTag(HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag[],System.Collections.Generic.List{System.Byte[]})" />
  public async Task<OperateResult> WriteTagAsync(S7Tag[] ids, List<byte[]> buffers)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildMultiSetRequest((IS7Object[]) ids, buffers));
    OperateResult operateResult = read.IsSuccess ? this.CheckReturnCode(read.Content) : (OperateResult) OperateResult.CreateFailedResult<List<S7Value>>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <summary>发送数据信息</summary>
  /// <param name="pdu">Pdu 报文信息</param>
  /// <returns>是否发送成功</returns>
  private OperateResult SendS7Pdu(byte[] pdu)
  {
    return this.CommunicationPipe.Send(SiemensS7Plus.BuildWithTPKTAndISO(pdu));
  }

  private void ReceiveS7Pdu(byte[] pdu)
  {
    this.LogNet?.WriteDebug(this.ToString(), "SSL/TLS: " + (this.LogMsgFormatBinary ? pdu.ToHexString(' ') : SoftBasic.GetAsciiStringRender(pdu)));
    int index = 0;
    if (pdu[index + 1] != (byte) 254 || pdu.Length <= 10)
      ;
    this.protoVersionBuffer = pdu[index + 1];
    while (index < pdu.Length && pdu[index] == (byte) 114)
    {
      int count = (int) this.ByteTransform.TransUInt16(pdu, index + 2);
      int offset = index + 4;
      if (count > 0 && this.protoVersionBuffer != (byte) 254)
        this.memoryStream.Write(pdu, offset, count);
      index = offset + count;
      if (count == 0)
      {
        this.recieveBuffer = this.memoryStream.ToArray();
        this.memoryStream = new MemoryStream();
        if (this.protoVersionBuffer != (byte) 254)
          this.autoResetEventPdu.Set();
      }
    }
  }

  private List<byte[]> BuildS7PlusPdu(byte[] pduData, int bytesToSend, byte protoVersion)
  {
    List<byte[]> numArrayList = new List<byte[]>();
    int sourceIndex = 0;
    int length1 = 1024 /*0x0400*/;
    byte[] array = new byte[length1];
    int num = length1 - 4 - 3 - 5 - 17 - 4 - 4;
    while (bytesToSend > 0)
    {
      int length2;
      if (bytesToSend > num)
      {
        length2 = num;
        bytesToSend -= num;
      }
      else
      {
        length2 = bytesToSend;
        bytesToSend -= length2;
      }
      array[0] = (byte) 114;
      array[1] = protoVersion;
      array[2] = BitConverter.GetBytes(length2)[1];
      array[3] = BitConverter.GetBytes(length2)[0];
      Array.Copy((Array) pduData, sourceIndex, (Array) array, 4, length2);
      sourceIndex += length2;
      int newSize = 4 + length2;
      if (bytesToSend == 0)
      {
        array[newSize] = (byte) 114;
        int index1 = newSize + 1;
        array[index1] = protoVersion;
        int index2 = index1 + 1;
        array[index2] = (byte) 0;
        int index3 = index2 + 1;
        array[index3] = (byte) 0;
        newSize = index3 + 1;
      }
      Array.Resize<byte>(ref array, newSize);
      numArrayList.Add(array);
    }
    return numArrayList;
  }

  private List<byte[]> BuildS7PlusPdu(byte[] pduData, byte protoVersion)
  {
    return this.BuildS7PlusPdu(pduData, pduData.Length, protoVersion);
  }

  private byte[] GetISOTelegrams()
  {
    this.ByteTransform.TransByte(this.LocalTSAP).CopyTo((Array) this.iso_head, 16 /*0x10*/);
    this.iso_head[3] = (byte) (20 + this.destTSAP.Length);
    this.iso_head[4] = (byte) (15 + this.destTSAP.Length);
    this.iso_head[19] = (byte) this.destTSAP.Length;
    return SoftBasic.SpliceArray<byte>(this.iso_head, this.destTSAP);
  }

  private byte[] BuildInitSslCommand()
  {
    byte[] pduData = new byte[18];
    pduData[0] = (byte) 49;
    pduData[3] = (byte) 5;
    pduData[4] = (byte) 179;
    int currentValue = (int) this.incrementCount.GetCurrentValue();
    pduData[7] = BitConverter.GetBytes(currentValue)[1];
    pduData[8] = BitConverter.GetBytes(currentValue)[0];
    pduData[13] = (byte) 48 /*0x30*/;
    return SiemensS7Plus.BuildWithTPKTAndISO(this.BuildS7PlusPdu(pduData, pduData.Length, (byte) 1)[0]);
  }

  private byte[] BuildCreateObjectRequest()
  {
    byte[] hexBytes = "31 00 00 04 CA 00 00 00 02 00 00 01 20 36 00 00 01 1D 00 04 00 00 00 00 00 A1 00 00 00 D3 82 1F 00 00 A3 82 2C 00 12 80 C3 C9 01 A1 00 00 00 D3 81 7F 00 00 A2 A2 00 00 00 00".ToHexBytes();
    int currentValue = (int) this.incrementCount.GetCurrentValue();
    hexBytes[7] = BitConverter.GetBytes(currentValue)[1];
    hexBytes[8] = BitConverter.GetBytes(currentValue)[0];
    return this.BuildS7PlusPdu(hexBytes, hexBytes.Length, (byte) 1)[0];
  }

  private byte[] BuildMultiSetRequest(byte[] structValue)
  {
    MemoryStream ms = new MemoryStream();
    byte[] hexBytes = "31 00 00 05 42 00 00 00 03 70 40 00 04 34 70 40 00 04 01 01 82 32 01".ToHexBytes();
    this.ByteTransform.TransByte(this.SessionID).CopyTo((Array) hexBytes, 9);
    this.ByteTransform.TransByte(this.SessionID).CopyTo((Array) hexBytes, 14);
    ms.Write(hexBytes);
    ms.Write(structValue);
    ms.Write("00 00 00 04 E8 89 69 00 12 00 00 00 00 89 6A 00 13 00 89 6B 00 04 00 ".ToHexBytes());
    uint num = 0;
    S7Object.WriteUint32(ms, num);
    ms.Write("00 00 00 00".ToHexBytes());
    byte[] array = ms.ToArray();
    int currentValue = (int) this.incrementCount.GetCurrentValue();
    array[7] = BitConverter.GetBytes(currentValue)[1];
    array[8] = BitConverter.GetBytes(currentValue)[0];
    return this.BuildS7PlusPdu(array, (byte) 2)[0];
  }

  private byte[] BuildMultiGetRequest(IS7Object[] s7Object)
  {
    MemoryStream ms = new MemoryStream();
    ms.Write("31 00 00 05 4C 00 00 00 03 70 40 00 04 34 00 00 00 00".ToHexBytes());
    S7Object.WriteUint32(ms, (uint) s7Object.Length);
    S7Object.WriteUint32(ms, (uint) ((IEnumerable<IS7Object>) s7Object).Sum<IS7Object>((Func<IS7Object, int>) (m => m.GetNumberOfFields())));
    for (int index = 0; index < s7Object.Length; ++index)
      s7Object[index].WriteMessgae(ms);
    return ms.ToArray();
  }

  private byte[] BuildMultiSetRequest(IS7Object[] s7Object, List<byte[]> buffers)
  {
    MemoryStream ms = new MemoryStream();
    ms.Write("31 00 00 05 42 00 00 00 03 70 40 00 04 34 00 00 00 00".ToHexBytes());
    S7Object.WriteUint32(ms, (uint) s7Object.Length);
    S7Object.WriteUint32(ms, (uint) ((IEnumerable<IS7Object>) s7Object).Sum<IS7Object>((Func<IS7Object, int>) (m => m.GetNumberOfFields())));
    for (int index = 0; index < s7Object.Length; ++index)
      s7Object[index].WriteMessgae(ms);
    for (int index = 0; index < buffers.Count; ++index)
    {
      S7Object.WriteUint32(ms, (uint) (index + 1));
      ms.Write(buffers[index]);
    }
    S7Object.WriteUint32(ms, 0U);
    return ms.ToArray();
  }

  private List<byte[]> BuildBrowseRequest(uint exploreId, uint[] attributes)
  {
    MemoryStream ms = new MemoryStream();
    ms.Write("31 00 00 04 BB 00 00 00 07 70 40 00 04 34 00 00 02 19 00 01 01 00 00".ToHexBytes());
    S7Object.WriteUint32(ms, (uint) attributes.Length);
    for (int index = 0; index < attributes.Length; ++index)
      S7Object.WriteUint32(ms, attributes[index]);
    uint currentValue1 = (uint) this.integrityIdCount.GetCurrentValue();
    S7Object.WriteUint32(ms, currentValue1);
    ms.Write("00 00 00 00 00".ToHexBytes());
    byte[] array = ms.ToArray();
    int currentValue2 = (int) this.incrementCount.GetCurrentValue();
    array[7] = BitConverter.GetBytes(currentValue2)[1];
    array[8] = BitConverter.GetBytes(currentValue2)[0];
    array[9] = BitConverter.GetBytes(this.SessionID)[3];
    array[10] = BitConverter.GetBytes(this.SessionID)[2];
    array[11] = BitConverter.GetBytes(this.SessionID)[1];
    array[12] = BitConverter.GetBytes(this.SessionID)[0];
    array[14] = BitConverter.GetBytes(exploreId)[3];
    array[15] = BitConverter.GetBytes(exploreId)[2];
    array[16 /*0x10*/] = BitConverter.GetBytes(exploreId)[1];
    array[17] = BitConverter.GetBytes(exploreId)[0];
    return this.BuildS7PlusPdu(array, array.Length, (byte) 2);
  }

  private S7Value ExtraS7Value(byte[] buffer, ref int index, bool ignoreID)
  {
    if (!ignoreID && S7Object.GetValueUint32(buffer, ref index) == 0U)
      return (S7Value) null;
    byte num1 = buffer[index++];
    byte num2 = buffer[index++];
    S7Value s7Value = new S7Value();
    s7Value.TypeCode = num2;
    s7Value.Flag = num1;
    switch (num1)
    {
      case 0:
        int num3;
        switch (num2)
        {
          case 1:
            s7Value.Value = (object) Convert.ToBoolean(buffer[index]);
            s7Value.Buffer = new byte[1]{ buffer[index] };
            ++index;
            return s7Value;
          case 2:
            num3 = 1;
            break;
          default:
            num3 = num2 == (byte) 10 ? 1 : 0;
            break;
        }
        if (num3 != 0)
        {
          s7Value.Value = (object) buffer[index];
          s7Value.Buffer = new byte[1]{ buffer[index] };
          ++index;
          return s7Value;
        }
        if (num2 == (byte) 3 || num2 == (byte) 11)
        {
          s7Value.Value = (object) this.ByteTransform.TransUInt16(buffer, index);
          s7Value.Buffer = buffer.SelectMiddle<byte>(index, 2);
          index += 2;
          return s7Value;
        }
        if (num2 == (byte) 4 || num2 == (byte) 8 || num2 == (byte) 19)
        {
          uint valueUint32 = S7Object.GetValueUint32(buffer, ref index);
          s7Value.Value = num2 != (byte) 4 ? (object) (int) valueUint32 : (object) valueUint32;
          s7Value.Buffer = this.ByteTransform.TransByte(valueUint32);
          return s7Value;
        }
        int num4;
        switch (num2)
        {
          case 5:
            ulong valueUint64_1 = S7Object.GetValueUint64(buffer, ref index);
            s7Value.Value = (object) valueUint64_1;
            s7Value.Buffer = this.ByteTransform.TransByte(valueUint64_1);
            return s7Value;
          case 6:
            s7Value.Value = (object) (sbyte) buffer[index];
            s7Value.Buffer = new byte[1]{ buffer[index] };
            ++index;
            return s7Value;
          case 7:
            s7Value.Value = (object) this.ByteTransform.TransInt16(buffer, index);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, 2);
            index += 2;
            return s7Value;
          case 9:
          case 13:
          case 16 /*0x10*/:
            num4 = 1;
            break;
          default:
            num4 = num2 == (byte) 17 ? 1 : 0;
            break;
        }
        if (num4 != 0)
        {
          ulong valueUint64_2 = S7Object.GetValueUint64(buffer, ref index);
          int num5 = num2 == (byte) 13 ? 1 : (num2 == (byte) 16 /*0x10*/ ? 1 : 0);
          s7Value.Value = num5 == 0 ? (object) (long) valueUint64_2 : (object) valueUint64_2;
          s7Value.Buffer = this.ByteTransform.TransByte(valueUint64_2);
          return s7Value;
        }
        if (num2 == (byte) 12 || num2 == (byte) 18)
        {
          s7Value.Value = (object) this.ByteTransform.TransUInt32(buffer, index);
          s7Value.Buffer = buffer.SelectMiddle<byte>(index, 4);
          index += 4;
          return s7Value;
        }
        if (num2 == (byte) 13 || num2 == (byte) 16 /*0x10*/)
        {
          s7Value.Value = (object) this.ByteTransform.TransUInt64(buffer, index);
          s7Value.Buffer = buffer.SelectMiddle<byte>(index, 8);
          index += 8;
          return s7Value;
        }
        switch (num2)
        {
          case 14:
            s7Value.Value = (object) this.ByteTransform.TransSingle(buffer, index);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, 4);
            index += 4;
            return s7Value;
          case 15:
            s7Value.Value = (object) this.ByteTransform.TransDouble(buffer, index);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, 8);
            index += 8;
            return s7Value;
          case 21:
            int valueUint32_1 = (int) S7Object.GetValueUint32(buffer, ref index);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, valueUint32_1);
            s7Value.Value = (object) Encoding.UTF8.GetString(s7Value.Buffer);
            index += valueUint32_1;
            return s7Value;
          case 23:
            s7Value.StructID = this.ByteTransform.TransUInt32(buffer, index);
            index += 4;
            index += 8;
            uint valueUint32_2 = S7Object.GetValueUint32(buffer, ref index);
            uint valueUint32_3 = S7Object.GetValueUint32(buffer, ref index);
            if ((valueUint32_2 & 1024U /*0x0400*/) > 0U)
              valueUint32_3 = S7Object.GetValueUint32(buffer, ref index);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, (int) valueUint32_3);
            index += (int) valueUint32_3;
            return s7Value;
        }
        break;
      case 16 /*0x10*/:
        if (num2 == (byte) 2 || num2 == (byte) 10)
        {
          int valueUint32_4 = (int) S7Object.GetValueUint32(buffer, ref index);
          s7Value.Buffer = buffer.SelectMiddle<byte>(index, valueUint32_4);
          s7Value.Value = (object) buffer.SelectMiddle<byte>(index, valueUint32_4);
          index += valueUint32_4;
          return s7Value;
        }
        if (num2 == (byte) 3 || num2 == (byte) 11)
        {
          int valueUint32_5 = (int) S7Object.GetValueUint32(buffer, ref index);
          s7Value.Value = (object) this.ByteTransform.TransUInt16(buffer, index, valueUint32_5);
          s7Value.Buffer = buffer.SelectMiddle<byte>(index, 2 * valueUint32_5);
          index += 2 * valueUint32_5;
          return s7Value;
        }
        switch (num2)
        {
          case 4:
            int valueUint32_6 = (int) S7Object.GetValueUint32(buffer, ref index);
            uint[] values1 = new uint[valueUint32_6];
            for (int index1 = 0; index1 < valueUint32_6; ++index1)
              values1[index1] = S7Object.GetValueUint32(buffer, ref index);
            s7Value.Value = (object) values1;
            s7Value.Buffer = this.ByteTransform.TransByte(values1);
            return s7Value;
          case 5:
            int valueUint32_7 = (int) S7Object.GetValueUint32(buffer, ref index);
            ulong[] values2 = new ulong[valueUint32_7];
            for (int index2 = 0; index2 < valueUint32_7; ++index2)
              values2[index2] = S7Object.GetValueUint64(buffer, ref index);
            s7Value.Value = (object) values2;
            s7Value.Buffer = this.ByteTransform.TransByte(values2);
            return s7Value;
          case 6:
            int valueUint32_8 = (int) S7Object.GetValueUint32(buffer, ref index);
            sbyte[] numArray = new sbyte[valueUint32_8];
            for (int index3 = 0; index3 < valueUint32_8; ++index3)
              numArray[index3] = (sbyte) buffer[index + index3];
            s7Value.Value = (object) numArray;
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, valueUint32_8);
            index += valueUint32_8;
            return s7Value;
          case 7:
            int valueUint32_9 = (int) S7Object.GetValueUint32(buffer, ref index);
            s7Value.Value = (object) this.ByteTransform.TransInt16(buffer, index, valueUint32_9);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, 2 * valueUint32_9);
            index += 2 * valueUint32_9;
            return s7Value;
          case 8:
            int valueUint32_10 = (int) S7Object.GetValueUint32(buffer, ref index);
            int[] values3 = new int[valueUint32_10];
            for (int index4 = 0; index4 < valueUint32_10; ++index4)
              values3[index4] = (int) S7Object.GetValueUint32(buffer, ref index);
            s7Value.Value = (object) values3;
            s7Value.Buffer = this.ByteTransform.TransByte(values3);
            return s7Value;
          case 9:
            int valueUint32_11 = (int) S7Object.GetValueUint32(buffer, ref index);
            long[] values4 = new long[valueUint32_11];
            for (int index5 = 0; index5 < valueUint32_11; ++index5)
              values4[index5] = (long) S7Object.GetValueUint64(buffer, ref index);
            s7Value.Value = (object) values4;
            s7Value.Buffer = this.ByteTransform.TransByte(values4);
            return s7Value;
          case 12:
            int valueUint32_12 = (int) S7Object.GetValueUint32(buffer, ref index);
            s7Value.Value = (object) this.ByteTransform.TransUInt32(buffer, index, valueUint32_12);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, 4 * valueUint32_12);
            index += 4 * valueUint32_12;
            return s7Value;
          case 13:
            int valueUint32_13 = (int) S7Object.GetValueUint32(buffer, ref index);
            s7Value.Value = (object) this.ByteTransform.TransUInt64(buffer, index, valueUint32_13);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, 8 * valueUint32_13);
            index += 8 * valueUint32_13;
            return s7Value;
          case 14:
            int valueUint32_14 = (int) S7Object.GetValueUint32(buffer, ref index);
            s7Value.Value = (object) this.ByteTransform.TransSingle(buffer, index, valueUint32_14);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, 4 * valueUint32_14);
            index += 4 * valueUint32_14;
            return s7Value;
          case 15:
            int valueUint32_15 = (int) S7Object.GetValueUint32(buffer, ref index);
            s7Value.Value = (object) this.ByteTransform.TransDouble(buffer, index, valueUint32_15);
            s7Value.Buffer = buffer.SelectMiddle<byte>(index, 8 * valueUint32_15);
            index += 8 * valueUint32_15;
            return s7Value;
        }
        break;
    }
    return s7Value;
  }

  private OperateResult<byte[]> ExtraAttributeData(byte[] buffer, uint attributeId, ref int index)
  {
    uint valueUint32_1 = S7Object.GetValueUint32(buffer, ref index);
    if (valueUint32_1 == 0U)
      return (OperateResult<byte[]>) null;
    byte num1 = buffer[index++];
    byte num2 = buffer[index++];
    switch (num1)
    {
      case 0:
        int num3;
        switch (num2)
        {
          case 1:
            ++index;
            if ((int) valueUint32_1 == (int) attributeId)
              return OperateResult.CreateSuccessResult<byte[]>(new byte[1]
              {
                buffer[index]
              });
            goto label_33;
          case 4:
            num3 = 1;
            break;
          default:
            num3 = num2 == (byte) 8 ? 1 : 0;
            break;
        }
        if (num3 != 0)
        {
          uint valueUint32_2 = S7Object.GetValueUint32(buffer, ref index);
          if ((int) valueUint32_1 == (int) attributeId)
            return OperateResult.CreateSuccessResult<byte[]>(this.ByteTransform.TransByte(valueUint32_2));
          break;
        }
        switch (num2)
        {
          case 3:
            index += 2;
            if ((int) valueUint32_1 == (int) attributeId)
              return OperateResult.CreateSuccessResult<byte[]>(buffer.SelectMiddle<byte>(index - 2, 2));
            break;
          case 5:
            ulong valueUint64 = S7Object.GetValueUint64(buffer, ref index);
            if ((int) valueUint32_1 == (int) attributeId)
              return OperateResult.CreateSuccessResult<byte[]>(this.ByteTransform.TransByte(valueUint64));
            break;
          case 11:
            index += 2;
            if ((int) valueUint32_1 == (int) attributeId)
              return OperateResult.CreateSuccessResult<byte[]>(buffer.SelectMiddle<byte>(index - 2, 2));
            break;
          case 12:
            index += 4;
            if ((int) valueUint32_1 == (int) attributeId)
              return OperateResult.CreateSuccessResult<byte[]>(buffer.SelectMiddle<byte>(index - 4, 4));
            break;
          case 21:
            int valueUint32_3 = (int) S7Object.GetValueUint32(buffer, ref index);
            index += valueUint32_3;
            if ((int) valueUint32_1 == (int) attributeId)
              return OperateResult.CreateSuccessResult<byte[]>(buffer.SelectMiddle<byte>(index - valueUint32_3, valueUint32_3));
            break;
          case 23:
            int index1 = index - 2;
            index += 4;
            for (OperateResult<byte[]> operateResult = this.ExtraAttributeData(buffer, attributeId, ref index); operateResult != null; operateResult = this.ExtraAttributeData(buffer, attributeId, ref index))
            {
              if (operateResult.IsSuccess && operateResult.Content != null)
                return operateResult;
            }
            if ((int) valueUint32_1 == (int) attributeId)
              return OperateResult.CreateSuccessResult<byte[]>(buffer.SelectMiddle<byte>(index1, index - index1));
            break;
        }
        break;
      case 16 /*0x10*/:
        if (num2 == (byte) 2)
        {
          int valueUint32_4 = (int) S7Object.GetValueUint32(buffer, ref index);
          index += valueUint32_4;
          if ((int) valueUint32_1 == (int) attributeId)
            return OperateResult.CreateSuccessResult<byte[]>(buffer.SelectMiddle<byte>(index - valueUint32_4, valueUint32_4));
        }
        break;
    }
label_33:
    return OperateResult.CreateSuccessResult<byte[]>((byte[]) null);
  }

  private OperateResult<byte[]> ExtraResponseData(byte[] buffer, uint attributeId, ref int index)
  {
    while (index < buffer.Length)
    {
      switch (buffer[index++])
      {
        case 161:
          index += 4;
          int valueUint32_1 = (int) S7Object.GetValueUint32(buffer, ref index);
          int valueUint32_2 = (int) S7Object.GetValueUint32(buffer, ref index);
          int valueUint32_3 = (int) S7Object.GetValueUint32(buffer, ref index);
          OperateResult<byte[]> operateResult1 = this.ExtraResponseData(buffer, attributeId, ref index);
          if (!operateResult1.IsSuccess || operateResult1.IsSuccess && operateResult1.Content != null)
            return operateResult1;
          break;
        case 163:
          OperateResult<byte[]> operateResult2 = this.ExtraAttributeData(buffer, attributeId, ref index);
          if (operateResult2.IsSuccess && operateResult2.Content != null)
            return operateResult2;
          break;
        default:
          goto label_8;
      }
    }
label_8:
    return new OperateResult<byte[]>($"No attribute id [{attributeId}] data");
  }

  private List<S7Object> ExploreResponse(byte[] buffer)
  {
    int index1 = 10;
    long valueUint64 = (long) S7Object.GetValueUint64(buffer, ref index1);
    int index2 = index1 + 4;
    int valueUint32 = (int) S7Object.GetValueUint32(buffer, ref index2);
    List<S7Object> s7ObjectList = new List<S7Object>();
    while (true)
    {
      if (buffer[index2] == (byte) 161)
        s7ObjectList.Add(this.CreateS7Object(buffer, ref index2));
      else
        break;
    }
    return s7ObjectList;
  }

  private OperateResult<List<S7Value>> ExploreS7Values(S7Tag[] s7Tags, byte[] buffer)
  {
    try
    {
      int index1 = 10;
      S7Object.GetValueUint64(buffer, ref index1);
      int index2 = 0;
      List<S7Value> s7ValueList = new List<S7Value>();
      while (true)
      {
        if (S7Object.GetValueUint32(buffer, ref index1) != 0U)
        {
          S7Value s7Value = this.ExtraS7Value(buffer, ref index1, true);
          if (s7Value != null)
          {
            if (index2 < s7Tags.Length && s7Tags[index2].TypeCode == (byte) 19 && s7Tags[index2].ArrayLength < 0)
              s7Value.Value = (object) Encoding.Default.GetString(s7Value.Buffer, 2, (int) s7Value.Buffer[1]);
            s7ValueList.Add(s7Value);
            ++index2;
          }
          else
            break;
        }
        else
          break;
      }
      return OperateResult.CreateSuccessResult<List<S7Value>>(s7ValueList);
    }
    catch (Exception ex)
    {
      return new OperateResult<List<S7Value>>("ExploreS7Values failed: " + ex.Message);
    }
  }

  private OperateResult CheckReturnCode(byte[] buffer)
  {
    try
    {
      int index = 10;
      ulong valueUint64 = S7Object.GetValueUint64(buffer, ref index);
      return valueUint64 == 0UL ? OperateResult.CreateSuccessResult() : new OperateResult($"ReturnCode: {valueUint64.ToString()} Source: {buffer.ToHexString(' ')}");
    }
    catch (Exception ex)
    {
      return new OperateResult("ExploreS7Values failed: " + ex.Message);
    }
  }

  private S7Object CreateS7Object(byte[] buffer, ref int index)
  {
    if (buffer[index] != (byte) 161)
      throw new Exception("Not S7 object: 0x" + buffer[index].ToString("X"));
    ++index;
    S7Object s7Object1 = new S7Object();
    s7Object1.RelationId = this.ByteTransform.TransUInt32(buffer, index);
    if (s7Object1.RelationId == 80U /*0x50*/)
      s7Object1.RelationId2 = 2415984640U /*0x90010000*/;
    if (s7Object1.RelationId == 81U)
      s7Object1.RelationId2 = 2416050176U /*0x90020000*/;
    if (s7Object1.RelationId == 82U)
      s7Object1.RelationId2 = 2416115712U /*0x90030000*/;
    if (s7Object1.RelationId == 83U)
      s7Object1.RelationId2 = 2416312320U /*0x90060000*/;
    if (s7Object1.RelationId == 84U)
      s7Object1.RelationId2 = 2416246784U /*0x90050000*/;
    index += 4;
    s7Object1.ClassId = S7Object.GetValueUint32(buffer, ref index);
    s7Object1.ClassFlags = S7Object.GetValueUint32(buffer, ref index);
    s7Object1.AttributeId = S7Object.GetValueUint32(buffer, ref index);
    if (buffer[index] == (byte) 163)
    {
      ++index;
      int valueUint32_1 = (int) S7Object.GetValueUint32(buffer, ref index);
      S7Object.GetValueUint32(buffer, ref index);
      uint valueUint32_2 = S7Object.GetValueUint32(buffer, ref index);
      if (valueUint32_2 != 21U)
        throw new Exception("Name get failed, not 0x15, actual: " + valueUint32_2.ToString("X"));
      uint valueUint32_3 = S7Object.GetValueUint32(buffer, ref index);
      s7Object1.Name = Encoding.UTF8.GetString(buffer, index, (int) valueUint32_3);
      index += (int) valueUint32_3;
    }
    OperateResult<byte[]> operateResult;
    while (true)
    {
      switch (buffer[index])
      {
        case 161:
          if (s7Object1.SubObjects == null)
            s7Object1.SubObjects = new List<S7Object>();
          S7Object s7Object2 = this.CreateS7Object(buffer, ref index);
          s7Object1.SubObjects.Add(s7Object2);
          break;
        case 162:
          goto label_21;
        case 163:
          ++index;
          operateResult = this.ExtraAttributeData(buffer, 0U, ref index);
          if (operateResult.IsSuccess)
            break;
          goto label_20;
        case 171:
          ++index;
          int num1 = (int) this.ByteTransform.TransUInt16(buffer, index);
          index += 2;
          s7Object1.S7Tags = new List<S7Tag>();
          int startIndex = index + 4;
          while (startIndex < index + num1)
          {
            S7Tag s7Tag = new S7Tag();
            s7Tag.LID = new List<uint>()
            {
              BitConverter.ToUInt32(buffer, startIndex)
            };
            s7Tag.TypeCode = buffer[startIndex + 8];
            int num2;
            switch ((int) buffer[startIndex + 9] >> 4)
            {
              case 0:
                num2 = 104;
                break;
              case 1:
              case 8:
                num2 = 4;
                break;
              case 2:
              case 9:
                num2 = 12;
                break;
              case 3:
              case 10:
                s7Tag.ArrayLength = BitConverter.ToInt32(buffer, startIndex + 28);
                num2 = 20;
                break;
              case 4:
              case 11:
                num2 = 68;
                break;
              case 5:
              case 12:
                s7Tag.StructID = BitConverter.ToUInt32(buffer, startIndex + 24);
                num2 = 32 /*0x20*/;
                break;
              case 6:
              case 13:
                num2 = 48 /*0x30*/;
                break;
              case 7:
              case 14:
                num2 = 96 /*0x60*/;
                break;
              default:
                num2 = 60;
                break;
            }
            s7Tag.StructOffset = num2 != 4 ? BitConverter.ToInt32(buffer, startIndex + 16 /*0x10*/) : (int) BitConverter.ToUInt16(buffer, startIndex + 14);
            startIndex += 12 + num2;
            s7Object1.S7Tags.Add(s7Tag);
          }
          index += num1;
          index += 2;
          break;
        case 172:
          ++index;
          int num3 = (int) this.ByteTransform.TransUInt16(buffer, index);
          index += 2;
          int index1 = 0;
          int index2 = index;
          while (index2 < index + num3)
          {
            byte count = buffer[index2];
            if (s7Object1.S7Tags != null && index1 < s7Object1.S7Tags.Count)
              s7Object1.S7Tags[index1].Name = Encoding.UTF8.GetString(buffer, index2 + 1, (int) count);
            index2 += 2 + (int) count;
            ++index1;
          }
          index += num3;
          index += 2;
          break;
        default:
          goto label_44;
      }
    }
label_20:
    throw new Exception("ExtraAttributeData failed: " + operateResult.Message);
label_21:
    ++index;
label_44:
    return s7Object1;
  }

  /// <inheritdoc />
  public override string ToString() => $"SiemensS7Plus[{this.IpAddress}:{this.Port}]";

  /// <summary>构建S7完整的通信报文</summary>
  /// <param name="pdu">报文信息</param>
  /// <returns>报文</returns>
  public static byte[] BuildWithTPKTAndISO(byte[] pdu)
  {
    byte[] numArray = SoftBasic.SpliceArray<byte>(new byte[7]
    {
      (byte) 3,
      (byte) 0,
      (byte) 0,
      (byte) 31 /*0x1F*/,
      (byte) 2,
      (byte) 240 /*0xF0*/,
      (byte) 128 /*0x80*/
    }, pdu);
    numArray[2] = BitConverter.GetBytes(numArray.Length)[1];
    numArray[3] = BitConverter.GetBytes(numArray.Length)[0];
    return numArray;
  }
}
