﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.NetSupport
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Security;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core;

/// <summary>
/// 静态的方法支持类，提供一些网络的静态支持，支持从套接字从同步接收指定长度的字节数据，并支持报告进度。<br />
/// The static method support class provides some static support for the network, supports receiving byte data of a specified length from the socket from synchronization, and supports reporting progress.
/// </summary>
/// <remarks>
/// 在接收指定数量的字节数据的时候，如果一直接收不到，就会发生假死的状态。接收的数据时保存在内存里的，不适合大数据块的接收。
/// </remarks>
public static class NetSupport
{
  /// <summary>
  /// Socket传输中的缓冲池大小<br />
  /// Buffer pool size in socket transmission
  /// </summary>
  internal const int SocketBufferSize = 16384 /*0x4000*/;

  /// <summary>根据接收数据的长度信息，合理的分割出单次的长度信息</summary>
  /// <param name="length">要接收数据的总长度信息</param>
  /// <returns>本次接收数据的长度</returns>
  internal static int GetSplitLengthFromTotal(int length)
  {
    if (length < 1024 /*0x0400*/)
      return length;
    if (length <= 8192 /*0x2000*/)
      return 2048 /*0x0800*/;
    if (length <= 32768 /*0x8000*/)
      return 8192 /*0x2000*/;
    if (length <= 262144 /*0x040000*/)
      return 32768 /*0x8000*/;
    if (length <= 1048576 /*0x100000*/)
      return 262144 /*0x040000*/;
    return length <= 8388608 /*0x800000*/ ? 1048576 /*0x100000*/ : 2097152 /*0x200000*/;
  }

  /// <summary>
  /// 从socket的网络中读取数据内容，需要指定数据长度和超时的时间，为了防止数据太大导致接收失败，所以此处接收到新的数据之后就更新时间。<br />
  /// To read the data content from the socket network, you need to specify the data length and timeout period. In order to prevent the data from being too large and cause the reception to fail, the time is updated after new data is received here.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="receive">接收的长度</param>
  /// <param name="reportProgress">当前接收数据的进度报告，有些协议支持传输非常大的数据内容，可以给与进度提示的功能</param>
  /// <returns>最终接收的指定长度的byte[]数据</returns>
  internal static byte[] ReadBytesFromSocket(
    Socket socket,
    int receive,
    Action<long, long> reportProgress = null)
  {
    byte[] buffer = new byte[receive];
    NetSupport.ReceiveBytesFromSocket(socket, buffer, 0, receive, reportProgress);
    return buffer;
  }

  /// <summary>
  /// 从socket的网络中读取数据内容，需要指定数据长度和超时的时间，为了防止数据太大导致接收失败，所以此处接收到新的数据之后就更新时间。<br />
  /// To read the data content from the socket network, you need to specify the data length and timeout period. In order to prevent the data from being too large and cause the reception to fail, the time is updated after new data is received here.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="buffer">缓存的字节数组</param>
  /// <param name="offset">偏移信息</param>
  /// <param name="length">接收长度</param>
  /// <param name="reportProgress">当前接收数据的进度报告，有些协议支持传输非常大的数据内容，可以给与进度提示的功能</param>
  /// <exception cref="T:HslCommunication.Core.RemoteCloseException">远程关闭的异常信息</exception>
  internal static void ReceiveBytesFromSocket(
    Socket socket,
    byte[] buffer,
    int offset,
    int length,
    Action<long, long> reportProgress = null)
  {
    int num1 = 0;
    while (num1 < length)
    {
      int size = Math.Min(length - num1, 16384 /*0x4000*/);
      int num2 = socket.Receive(buffer, num1 + offset, size, SocketFlags.None);
      num1 += num2;
      if (num2 == 0)
        throw new RemoteCloseException();
      if (reportProgress != null)
        reportProgress((long) num1, (long) length);
    }
  }

  /// <summary>从socket的网络中读取数据内容，然后写入到流中</summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="stream">等待写入的流</param>
  /// <param name="length">长度信息</param>
  /// <param name="reportProgress">当前接收数据的进度报告，有些协议支持传输非常大的数据内容，可以给与进度提示的功能</param>
  /// <exception cref="T:HslCommunication.Core.RemoteCloseException">远程关闭的异常信息</exception>
  internal static void ReceiveBytesFromSocket(
    Socket socket,
    Stream stream,
    int length,
    Action<long, long> reportProgress = null)
  {
    int num = 0;
    byte[] buffer = new byte[NetSupport.GetSplitLengthFromTotal(length)];
    while (num < length)
    {
      int count = socket.Receive(buffer, 0, buffer.Length, SocketFlags.None);
      stream.Write(buffer, 0, count);
      num += count;
      if (count == 0)
        throw new RemoteCloseException();
      if (reportProgress != null)
        reportProgress((long) num, (long) length);
    }
  }

  /// <summary>
  /// 创建一个新的socket对象并连接到远程的地址，需要指定远程终结点，超时时间（单位是毫秒），如果需要绑定本地的IP或是端口，传入 local对象<br />
  /// To create a new socket object and connect to the remote address, you need to specify the remote endpoint,
  /// the timeout period (in milliseconds), if you need to bind the local IP or port, pass in the local object
  /// </summary>
  /// <param name="ipAddress">IP地址信息，支持Ipv4，Ipv6，以及域名</param>
  /// <param name="port">端口号信息</param>
  /// <param name="timeOut">连接的超时时间</param>
  /// <param name="local">如果需要绑定本地的IP地址，就需要设置当前的对象</param>
  /// <returns>返回套接字的封装结果对象</returns>
  /// <example>
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="CreateSocketAndConnectExample" title="创建连接示例" />
  /// </example>
  internal static OperateResult<Socket> CreateSocketAndConnect(
    string ipAddress,
    int port,
    int timeOut,
    IPEndPoint local = null)
  {
    return NetSupport.CreateSocketAndConnect(new IPEndPoint(IPAddress.Parse(HslHelper.GetIpAddressFromInput(ipAddress)), port), timeOut, local);
  }

  /// <summary>
  /// 创建一个新的socket对象并连接到远程的地址，需要指定远程终结点，超时时间（单位是毫秒），如果需要绑定本地的IP或是端口，传入 local对象<br />
  /// To create a new socket object and connect to the remote address, you need to specify the remote endpoint,
  /// the timeout period (in milliseconds), if you need to bind the local IP or port, pass in the local object
  /// </summary>
  /// <param name="endPoint">连接的目标终结点</param>
  /// <param name="timeOut">连接的超时时间</param>
  /// <param name="local">如果需要绑定本地的IP地址，就需要设置当前的对象</param>
  /// <returns>返回套接字的封装结果对象</returns>
  /// <example>
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="CreateSocketAndConnectExample" title="创建连接示例" />
  /// </example>
  internal static OperateResult<Socket> CreateSocketAndConnect(
    IPEndPoint endPoint,
    int timeOut,
    IPEndPoint local = null)
  {
    int num = 0;
    while (true)
    {
      ++num;
      Socket socket;
      try
      {
        socket = new Socket(endPoint.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
        socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
      }
      catch (Exception ex)
      {
        return new OperateResult<Socket>("Socket Create Exception -> " + ex.Message);
      }
      HslTimeOut hslTimeOut = HslTimeOut.HandleTimeOutCheck(socket, timeOut);
      try
      {
        if (local != null)
          socket.Bind((EndPoint) local);
        socket.Connect((EndPoint) endPoint);
        hslTimeOut.IsSuccessful = true;
        return OperateResult.CreateSuccessResult<Socket>(socket);
      }
      catch (Exception ex)
      {
        socket?.Close();
        hslTimeOut.IsSuccessful = true;
        if (hslTimeOut.GetConsumeTime() < TimeSpan.FromMilliseconds(500.0) && num < 2)
          HslHelper.ThreadSleep(100);
        else
          return hslTimeOut.IsTimeout ? new OperateResult<Socket>(string.Format(StringResources.Language.ConnectTimeout, (object) endPoint, (object) timeOut) + " ms") : new OperateResult<Socket>($"Socket Connect {endPoint} Exception -> " + ex.Message);
      }
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteDevice.ReadFromCoreServer(System.Collections.Generic.IEnumerable{System.Byte[]})" />
  public static OperateResult<byte[]> ReadFromCoreServer(
    IEnumerable<byte[]> send,
    Func<byte[], OperateResult<byte[]>> funcRead)
  {
    List<byte> byteList = new List<byte>();
    foreach (byte[] numArray in send)
    {
      OperateResult<byte[]> operateResult = funcRead(numArray);
      if (!operateResult.IsSuccess)
        return operateResult;
      if (operateResult.Content != null)
        byteList.AddRange((IEnumerable<byte>) operateResult.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.CreateSocketAndConnect(System.Net.IPEndPoint,System.Int32,System.Net.IPEndPoint)" />
  internal static async Task<OperateResult<Socket>> CreateSocketAndConnectAsync(
    IPEndPoint endPoint,
    int timeOut,
    IPEndPoint local = null)
  {
    int connectCount = 0;
    HslTimeOut connectTimeout;
    Exception ex;
    while (true)
    {
      ++connectCount;
      Socket socket = (Socket) null;
      try
      {
        socket = new Socket(endPoint.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
        socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
      }
      catch (Exception ex1)
      {
        return new OperateResult<Socket>("Socket Create Exception -> " + ex1.Message);
      }
      connectTimeout = HslTimeOut.HandleTimeOutCheck(socket, timeOut);
      int num = 0;
      object obj;
      try
      {
        if (local != null)
          socket.Bind((EndPoint) local);
        await Task.Factory.FromAsync(socket.BeginConnect((EndPoint) endPoint, (AsyncCallback) null, (object) socket), new Action<IAsyncResult>(socket.EndConnect)).ConfigureAwait(false);
        connectTimeout.IsSuccessful = true;
        return OperateResult.CreateSuccessResult<Socket>(socket);
      }
      catch (Exception ex2)
      {
        obj = (object) ex2;
        num = 1;
      }
      if (num == 1)
      {
        ex = (Exception) obj;
        connectTimeout.IsSuccessful = true;
        socket?.Close();
        if (connectTimeout.GetConsumeTime() < TimeSpan.FromMilliseconds(500.0) && connectCount < 2)
          await Task.Delay(100);
        else
          break;
      }
      else
      {
        obj = (object) null;
        socket = (Socket) null;
        connectTimeout = (HslTimeOut) null;
      }
    }
    return !connectTimeout.IsTimeout ? new OperateResult<Socket>("Socket Exception -> " + ex.Message) : new OperateResult<Socket>(string.Format(StringResources.Language.ConnectTimeout, (object) endPoint, (object) timeOut) + " ms");
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteDevice.ReadFromCoreServer(System.Collections.Generic.IEnumerable{System.Byte[]})" />
  public static async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    IEnumerable<byte[]> send,
    Func<byte[], Task<OperateResult<byte[]>>> funcRead)
  {
    List<byte> array = new List<byte>();
    foreach (byte[] data in send)
    {
      OperateResult<byte[]> read = await funcRead(data).ConfigureAwait(false);
      if (!read.IsSuccess)
        return read;
      if (read.Content != null)
        array.AddRange((IEnumerable<byte>) read.Content);
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(array.ToArray());
  }

  /// <summary>关闭指定的socket套接字对象</summary>
  /// <param name="socket">套接字对象</param>
  public static void CloseSocket(Socket socket)
  {
    try
    {
      socket?.Close();
    }
    catch
    {
    }
  }

  /// <summary>
  /// 表示Socket发生异常的错误码<br />
  /// An error code indicates that an exception has occurred in the socket
  /// </summary>
  public static int SocketErrorCode { get; } = -1;

  /// <summary>
  /// 创建接收数据的缓存，并返回是否创建成功<br />
  /// Create a cache that receives data and return whether the creation is successful
  /// </summary>
  /// <param name="length">准备创建的长度信息，如果传入负数，则自动创建长度 2048 的缓存</param>
  /// <returns>创建成功的缓存</returns>
  public static OperateResult<byte[]> CreateReceiveBuffer(int length)
  {
    int length1 = length >= 0 ? length : 2048 /*0x0800*/;
    try
    {
      return OperateResult.CreateSuccessResult<byte[]>(new byte[length1]);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"Create byte[{length1}] buffer failed: {ex.Message}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketReceive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static OperateResult<byte[]> SocketReceive(
    Socket socket,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    OperateResult<byte[]> receiveBuffer = NetSupport.CreateReceiveBuffer(length);
    if (!receiveBuffer.IsSuccess)
      return receiveBuffer;
    OperateResult<int> result = NetSupport.SocketReceive(socket, receiveBuffer.Content, 0, length, timeOut, reportProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(length > 0 ? receiveBuffer.Content : receiveBuffer.Content.SelectBegin<byte>(result.Content));
  }

  /// <summary>
  /// 接收固定长度的字节数组，允许指定超时时间，默认为60秒，当length大于0时，接收固定长度的数据内容，当length小于0时，buffer长度的缓存数据<br />
  /// Receiving a fixed-length byte array, allowing a specified timeout time. The default is 60 seconds. When length is greater than 0,
  /// fixed-length data content is received. When length is less than 0, random data information of a length not greater than 2048 is received.
  /// </summary>
  /// <param name="socket">网络通讯的套接字<br />Network communication socket</param>
  /// <param name="buffer">等待接收的数据缓存信息</param>
  /// <param name="offset">开始接收数据的偏移地址</param>
  /// <param name="length">准备接收的数据长度，当length大于0时，接收固定长度的数据内容，当length小于0时，接收不大于1024长度的随机数据信息</param>
  /// <param name="timeOut">单位：毫秒，超时时间，默认为60秒，如果设置小于0，则不检查超时时间</param>
  /// <param name="reportProgress">当前接收数据的进度报告，有些协议支持传输非常大的数据内容，可以给与进度提示的功能</param>
  /// <returns>包含了字节数据的结果类</returns>
  public static OperateResult<int> SocketReceive(
    Socket socket,
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(0);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    try
    {
      socket.ReceiveTimeout = timeOut;
      if (length > 0)
      {
        NetSupport.ReceiveBytesFromSocket(socket, buffer, offset, length, reportProgress);
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int num = socket.Receive(buffer, offset, buffer.Length - offset, SocketFlags.None);
      return num != 0 ? OperateResult.CreateSuccessResult<int>(num) : throw new RemoteCloseException();
    }
    catch (RemoteCloseException ex)
    {
      return new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + StringResources.Language.RemoteClosedConnection);
    }
    catch (SocketException ex)
    {
      if (ex.SocketErrorCode != SocketError.TimedOut)
        return new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + ex.Message);
      return new OperateResult<int>(NetSupport.SocketErrorCode, $"Socket Exception -> {ex.Message} Timeout: {timeOut}")
      {
        Content = int.MaxValue
      };
    }
    catch (Exception ex)
    {
      return new OperateResult<int>(NetSupport.SocketErrorCode, "Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketReceive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static async Task<OperateResult<byte[]>> SocketReceiveAsync(
    Socket socket,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    OperateResult<byte[]> createBuffer = NetSupport.CreateReceiveBuffer(length);
    if (!createBuffer.IsSuccess)
      return createBuffer;
    OperateResult<int> receive = await NetSupport.SocketReceiveAsync(socket, createBuffer.Content, 0, length, timeOut, reportProgress).ConfigureAwait(false);
    return receive.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(length > 0 ? createBuffer.Content : createBuffer.Content.SelectBegin<byte>(receive.Content)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) receive);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketReceive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static async Task<OperateResult<int>> SocketReceiveAsync(
    Socket socket,
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(length);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
    {
      OperateResult<int> operateResult = new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    }
    HslTimeOut hslTimeOut = HslTimeOut.HandleTimeOutCheck(socket, timeOut);
    try
    {
      if (length > 0)
      {
        int alreadyCount = 0;
        do
        {
          int currentReceiveLength = length - alreadyCount > 16384 /*0x4000*/ ? 16384 /*0x4000*/ : length - alreadyCount;
          int count = await Task.Factory.FromAsync<int>(socket.BeginReceive(buffer, alreadyCount + offset, currentReceiveLength, SocketFlags.None, (AsyncCallback) null, (object) socket), new Func<IAsyncResult, int>(socket.EndReceive)).ConfigureAwait(false);
          alreadyCount += count;
          if (count > 0)
          {
            hslTimeOut.StartTime = DateTime.Now;
            Action<long, long> action = reportProgress;
            if (action != null)
              action((long) alreadyCount, (long) length);
          }
          else
            goto label_9;
        }
        while (alreadyCount < length);
        goto label_13;
label_9:
        throw new RemoteCloseException();
label_13:
        hslTimeOut.IsSuccessful = true;
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int count1 = await Task.Factory.FromAsync<int>(socket.BeginReceive(buffer, offset, buffer.Length - offset, SocketFlags.None, (AsyncCallback) null, (object) socket), new Func<IAsyncResult, int>(socket.EndReceive)).ConfigureAwait(false);
      if (count1 == 0)
        throw new RemoteCloseException();
      hslTimeOut.IsSuccessful = true;
      return OperateResult.CreateSuccessResult<int>(count1);
    }
    catch (RemoteCloseException ex)
    {
      socket?.Close();
      hslTimeOut.IsSuccessful = true;
      return new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + StringResources.Language.RemoteClosedConnection);
    }
    catch (Exception ex)
    {
      socket?.Close();
      hslTimeOut.IsSuccessful = true;
      return !hslTimeOut.IsTimeout ? new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + ex.Message) : new OperateResult<int>(NetSupport.SocketErrorCode, $"Socket Exception -> {StringResources.Language.ReceiveDataTimeout}{hslTimeOut.DelayTime}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketReceive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static async Task<OperateResult<int>> SocketReceiveAsync2(
    Socket socket,
    byte[] buffer,
    int offset,
    int length,
    int timeOut,
    Action<long, long> reportProgress,
    Func<IAsyncResult, int> endMethod)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(length);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
    {
      OperateResult<int> operateResult = new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    }
    HslTimeOut hslTimeOut = HslTimeOut.HandleTimeOutCheck(socket, timeOut);
    try
    {
      if (length > 0)
      {
        int alreadyCount = 0;
        do
        {
          int currentReceiveLength = length - alreadyCount > 16384 /*0x4000*/ ? 16384 /*0x4000*/ : length - alreadyCount;
          int count = await Task.Factory.FromAsync<int>(socket.BeginReceive(buffer, alreadyCount + offset, currentReceiveLength, SocketFlags.None, (AsyncCallback) null, (object) socket), endMethod).ConfigureAwait(false);
          alreadyCount += count;
          if (count > 0)
          {
            hslTimeOut.StartTime = DateTime.Now;
            Action<long, long> action = reportProgress;
            if (action != null)
              action((long) alreadyCount, (long) length);
          }
          else
            goto label_9;
        }
        while (alreadyCount < length);
        goto label_13;
label_9:
        throw new RemoteCloseException();
label_13:
        hslTimeOut.IsSuccessful = true;
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int count1 = await Task.Factory.FromAsync<int>(socket.BeginReceive(buffer, offset, buffer.Length - offset, SocketFlags.None, (AsyncCallback) null, (object) socket), endMethod).ConfigureAwait(false);
      if (count1 == 0)
        throw new RemoteCloseException();
      hslTimeOut.IsSuccessful = true;
      return OperateResult.CreateSuccessResult<int>(count1);
    }
    catch (RemoteCloseException ex)
    {
      socket?.Close();
      hslTimeOut.IsSuccessful = true;
      return new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + StringResources.Language.RemoteClosedConnection);
    }
    catch (Exception ex)
    {
      socket?.Close();
      hslTimeOut.IsSuccessful = true;
      return !hslTimeOut.IsTimeout ? new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + ex.Message) : new OperateResult<int>(NetSupport.SocketErrorCode, $"Socket Exception -> {StringResources.Language.ReceiveDataTimeout}{hslTimeOut.DelayTime}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketReceive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static OperateResult<byte[]> SocketReceive(
    SslStream ssl,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    OperateResult<byte[]> receiveBuffer = NetSupport.CreateReceiveBuffer(length);
    if (!receiveBuffer.IsSuccess)
      return receiveBuffer;
    OperateResult<int> result = NetSupport.SocketReceive(ssl, receiveBuffer.Content, 0, length, timeOut, reportProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(length > 0 ? receiveBuffer.Content : receiveBuffer.Content.SelectBegin<byte>(result.Content));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketReceive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static OperateResult<int> SocketReceive(
    SslStream ssl,
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(0);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    try
    {
      ssl.ReadTimeout = timeOut;
      if (length > 0)
      {
        int num1 = 0;
        while (num1 < length)
        {
          int count = Math.Min(length - num1, 16384 /*0x4000*/);
          int num2 = ssl.Read(buffer, num1 + offset, count);
          num1 += num2;
          if (num2 == 0)
            throw new RemoteCloseException();
          if (reportProgress != null)
            reportProgress((long) num1, (long) length);
        }
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int num = ssl.Read(buffer, offset, buffer.Length - offset);
      return num != 0 ? OperateResult.CreateSuccessResult<int>(num) : throw new RemoteCloseException();
    }
    catch (RemoteCloseException ex)
    {
      ssl?.Close();
      return new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + StringResources.Language.RemoteClosedConnection);
    }
    catch (Exception ex)
    {
      ssl?.Close();
      return new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketReceive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static async Task<OperateResult<byte[]>> SocketReceiveAsync(
    SslStream ssl,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    OperateResult<byte[]> createBuffer = NetSupport.CreateReceiveBuffer(length);
    if (!createBuffer.IsSuccess)
      return createBuffer;
    OperateResult<int> receive = await NetSupport.SocketReceiveAsync(ssl, createBuffer.Content, 0, length, timeOut, reportProgress).ConfigureAwait(false);
    return receive.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(length > 0 ? createBuffer.Content : createBuffer.Content.SelectBegin<byte>(receive.Content)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) receive);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketReceive(System.Net.Security.SslStream,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static async Task<OperateResult<int>> SocketReceiveAsync(
    SslStream ssl,
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(length);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
    {
      OperateResult<int> operateResult = new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    }
    try
    {
      if (length > 0)
      {
        int alreadyCount = 0;
        do
        {
          int currentReceiveLength = length - alreadyCount > 16384 /*0x4000*/ ? 16384 /*0x4000*/ : length - alreadyCount;
          int count = await ssl.ReadAsync(buffer, alreadyCount + offset, currentReceiveLength).ConfigureAwait(false);
          alreadyCount += count;
          if (count != 0)
          {
            Action<long, long> action = reportProgress;
            if (action != null)
              action((long) alreadyCount, (long) length);
          }
          else
            goto label_8;
        }
        while (alreadyCount < length);
        goto label_13;
label_8:
        throw new RemoteCloseException();
label_13:
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int count1 = await ssl.ReadAsync(buffer, offset, buffer.Length - offset).ConfigureAwait(false);
      return count1 != 0 ? OperateResult.CreateSuccessResult<int>(count1) : throw new RemoteCloseException();
    }
    catch (RemoteCloseException ex)
    {
      ssl?.Close();
      return new OperateResult<int>(NetSupport.SocketErrorCode, StringResources.Language.RemoteClosedConnection);
    }
    catch (Exception ex)
    {
      ssl?.Close();
      return new OperateResult<int>(NetSupport.SocketErrorCode, "Socket Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketSend(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  public static OperateResult SocketSend(Socket socket, byte[] data)
  {
    return data == null ? OperateResult.CreateSuccessResult() : NetSupport.SocketSend(socket, data, 0, data.Length);
  }

  /// <summary>
  /// 发送消息给套接字，直到完成的时候返回，经过测试，本方法是线程安全的。<br />
  /// Send a message to the socket until it returns when completed. After testing, this method is thread-safe.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="data">字节数据</param>
  /// <param name="offset">偏移的位置信息</param>
  /// <param name="size">发送的数据总数</param>
  /// <returns>发送是否成功的结果</returns>
  public static OperateResult SocketSend(Socket socket, byte[] data, int offset, int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    if (socket == null)
      return (OperateResult) new OperateResult<byte[]>(NetSupport.SocketErrorCode, "Socket is null");
    try
    {
      int num1 = 0;
      do
      {
        int num2 = socket.Send(data, offset, size - num1, SocketFlags.None);
        num1 += num2;
        offset += num2;
      }
      while (num1 < size && num1 < data.Length);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      socket?.Close();
      return (OperateResult) new OperateResult<byte[]>(NetSupport.SocketErrorCode, ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketSend(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  public static async Task<OperateResult> SocketSendAsync(Socket socket, byte[] data)
  {
    if (data == null)
    {
      OperateResult operateResult = await Task.FromResult<OperateResult>(OperateResult.CreateSuccessResult()).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult operateResult1 = await NetSupport.SocketSendAsync(socket, data, 0, data.Length).ConfigureAwait(false);
    return operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketSend(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  public static async Task<OperateResult> SocketSendAsync(
    Socket socket,
    byte[] data,
    int offset,
    int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult(StringResources.Language.AuthorizationFailed);
    if (socket == null)
      return (OperateResult) new OperateResult<byte[]>(NetSupport.SocketErrorCode, "Socket is null");
    int sendCount = 0;
    try
    {
      do
      {
        int count = await Task.Factory.FromAsync<int>(socket.BeginSend(data, offset, size - sendCount, SocketFlags.None, (AsyncCallback) null, (object) socket), new Func<IAsyncResult, int>(socket.EndSend)).ConfigureAwait(false);
        sendCount += count;
        offset += count;
      }
      while (sendCount < size && sendCount < data.Length);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      socket?.Close();
      return (OperateResult) new OperateResult<byte[]>(NetSupport.SocketErrorCode, ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketSend(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  public static OperateResult SocketSend(SslStream ssl, byte[] data)
  {
    return data == null ? OperateResult.CreateSuccessResult() : NetSupport.SocketSend(ssl, data, 0, data.Length);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketSend(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  public static OperateResult SocketSend(SslStream ssl, byte[] data, int offset, int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    if (ssl == null)
      return new OperateResult(NetSupport.SocketErrorCode, "SslStream is null");
    try
    {
      ssl.Write(data, offset, size);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      ssl?.Close();
      return (OperateResult) new OperateResult<byte[]>(NetSupport.SocketErrorCode, ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketSend(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  public static async Task<OperateResult> SocketSendAsync(SslStream ssl, byte[] data)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    OperateResult operateResult = await NetSupport.SocketSendAsync(ssl, data, 0, data.Length).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.SocketSend(System.Net.Security.SslStream,System.Byte[],System.Int32,System.Int32)" />
  public static async Task<OperateResult> SocketSendAsync(
    SslStream ssl,
    byte[] data,
    int offset,
    int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    if (ssl == null)
      return new OperateResult(NetSupport.SocketErrorCode, "SslStream is null");
    try
    {
      await ssl.WriteAsync(data, offset, size).ConfigureAwait(false);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      ssl?.Close();
      return (OperateResult) new OperateResult<byte[]>(NetSupport.SocketErrorCode, ex.Message);
    }
  }

  /// <summary>
  /// 读取流中的数据到缓存区，读取的长度需要按照实际的情况来判断<br />
  /// Read the data in the stream to the buffer area. The length of the read needs to be determined according to the actual situation.
  /// </summary>
  /// <param name="stream">数据流</param>
  /// <param name="buffer">缓冲区</param>
  /// <returns>带有成功标志的读取数据长度</returns>
  public static OperateResult<int> ReadStream(Stream stream, byte[] buffer)
  {
    ManualResetEvent manualResetEvent = new ManualResetEvent(false);
    FileStateObject fileStateObject = new FileStateObject();
    fileStateObject.WaitDone = manualResetEvent;
    fileStateObject.Stream = stream;
    fileStateObject.DataLength = buffer.Length;
    fileStateObject.Buffer = buffer;
    FileStateObject state = fileStateObject;
    try
    {
      stream.BeginRead(buffer, 0, state.DataLength, new AsyncCallback(NetSupport.ReadStreamCallBack), (object) state);
    }
    catch (Exception ex)
    {
      manualResetEvent.Close();
      return new OperateResult<int>("stream.BeginRead Exception -> " + ex.Message);
    }
    manualResetEvent.WaitOne();
    manualResetEvent.Close();
    return state.IsError ? new OperateResult<int>(state.ErrerMsg) : OperateResult.CreateSuccessResult<int>(state.AlreadyDealLength);
  }

  private static void ReadStreamCallBack(IAsyncResult ar)
  {
    if (!(ar.AsyncState is FileStateObject asyncState))
      return;
    try
    {
      asyncState.AlreadyDealLength += asyncState.Stream.EndRead(ar);
      asyncState.WaitDone.Set();
    }
    catch (Exception ex)
    {
      asyncState.IsError = true;
      asyncState.ErrerMsg = ex.Message;
      asyncState.WaitDone.Set();
    }
  }

  /// <summary>
  /// 将缓冲区的数据写入到流里面去<br />
  /// Write the buffer data to the stream
  /// </summary>
  /// <param name="stream">数据流</param>
  /// <param name="buffer">缓冲区</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteStream(Stream stream, byte[] buffer)
  {
    ManualResetEvent manualResetEvent = new ManualResetEvent(false);
    FileStateObject fileStateObject = new FileStateObject();
    fileStateObject.WaitDone = manualResetEvent;
    fileStateObject.Stream = stream;
    FileStateObject state = fileStateObject;
    try
    {
      stream.BeginWrite(buffer, 0, buffer.Length, new AsyncCallback(NetSupport.WriteStreamCallBack), (object) state);
    }
    catch (Exception ex)
    {
      manualResetEvent.Close();
      return new OperateResult("stream.BeginWrite Exception -> " + ex.Message);
    }
    manualResetEvent.WaitOne();
    manualResetEvent.Close();
    if (!state.IsError)
      return OperateResult.CreateSuccessResult();
    return new OperateResult() { Message = state.ErrerMsg };
  }

  private static void WriteStreamCallBack(IAsyncResult ar)
  {
    if (!(ar.AsyncState is FileStateObject asyncState))
      return;
    try
    {
      asyncState.Stream.EndWrite(ar);
    }
    catch (Exception ex)
    {
      asyncState.IsError = true;
      asyncState.ErrerMsg = ex.Message;
    }
    finally
    {
      asyncState.WaitDone.Set();
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.ReadStream(System.IO.Stream,System.Byte[])" />
  public static async Task<OperateResult<int>> ReadStreamAsync(Stream stream, byte[] buffer)
  {
    int num = 0;
    if (num != 0 && !HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    try
    {
      int count = await stream.ReadAsync(buffer, 0, buffer.Length).ConfigureAwait(false);
      return OperateResult.CreateSuccessResult<int>(count);
    }
    catch (Exception ex)
    {
      stream?.Close();
      return new OperateResult<int>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.NetSupport.WriteStream(System.IO.Stream,System.Byte[])" />
  public static async Task<OperateResult> WriteStreamAsync(Stream stream, byte[] buffer)
  {
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult(StringResources.Language.AuthorizationFailed);
    int alreadyCount = 0;
    try
    {
      await stream.WriteAsync(buffer, alreadyCount, buffer.Length - alreadyCount).ConfigureAwait(false);
      return (OperateResult) OperateResult.CreateSuccessResult<int>(alreadyCount);
    }
    catch (Exception ex)
    {
      stream?.Close();
      return (OperateResult) new OperateResult<int>(ex.Message);
    }
  }
}
